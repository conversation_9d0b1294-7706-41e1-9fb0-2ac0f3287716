using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Cysharp.Threading.Tasks;
using Spine.Unity;
using UnityEngine;
using UnityEngine.U2D;
using YooAsset;

public class BattleResources
{
    private static BattleResources _inst;
    public static BattleResources Inst
    {
        get
        {
            _inst ??= new BattleResources();
            return _inst;
        }
    }

    // public Texture themeTexture;
    public Sprite themeBg;
    public SpriteAtlas iconAtlas;
    public SubAssetsOperationHandle iconAtlasHandle;
    public Sprite firecracker;

    private Dictionary<int, Texture> _blockTextures = new();

    async public UniTask PreLoad(Action<float> onProgress)
    {
        // 初始化进度为0
        onProgress?.Invoke(0f);

        var themeId = SystemFacade.SkinSystem.GetCurrentThemeId();
        var themeDir = $"theme{themeId}";
        // var themeIconTask = LoadTextureAsync($"Theme/{themeDir}/theme{themeId}");
        var themeBgTask = LoadSpriteAsync($"Theme/{themeDir}/themeBg{themeId}");
        var firecrackerTask = LoadSpriteAsync($"Textures/firecracker");
        var iconAtlasTask = LoadSpriteAtlasAsync($"Theme/{themeDir}/theme{themeId}");

        // 创建所有任务的列表，用于跟踪进度
        var allTasks = new List<UniTask>
        {
            // themeIconTask.AsUniTask(),
            iconAtlasTask.AsUniTask(),
            themeBgTask.AsUniTask(),
            firecrackerTask.AsUniTask(),
            InitPool()
        };

        // 添加所有纹理加载任务
        allTasks.Add(LoadBlockTexture(Tile3D.TYPE_BLOCK, $"Textures/brick{themeId}"));
        // allTasks.Add(LoadBlockTexture(Tile3D.TYPE_QUESTION, $"Textures/question"));
        allTasks.Add(LoadBlockTexture(Tile3D.TYPE_FREEZE1, $"Textures/freeze1"));
        allTasks.Add(LoadBlockTexture(Tile3D.TYPE_FREEZE2, $"Textures/freeze2"));
        allTasks.Add(LoadBlockTexture(Tile3D.TYPE_FREEZE3, $"Textures/freeze3"));
        allTasks.Add(LoadBlockTexture(Tile3D.TYPE_CHAIN, $"Textures/chain"));
        allTasks.Add(LoadBlockTexture(Tile3D.TYPE_WOOD, $"Textures/wood"));
        allTasks.Add(LoadBlockTexture(Tile3D.TYPE_STONE, $"Textures/stone"));

        // allTasks.Add(LoadBlockTexture(Tile3D.TYPE_FIRECRACKER, $"Textures/firecracker"));

        // 总任务数
        int totalTasks = allTasks.Count;

        // 逐个执行任务并报告进度
        for (int i = 0; i < allTasks.Count; i++)
        {
            // 等待当前任务完成
            await allTasks[i];

            // 计算并报告进度
            float progress = (float)(i + 1) / totalTasks;
            onProgress?.Invoke(progress);
        }

        // 报告100%进度
        onProgress?.Invoke(1f);

        // themeTexture = await themeIconTask;
        // if (themeTexture == null)
        // {
        //     themeTexture = await LoadTextureAsync($"Theme/theme1");
        // }
        iconAtlasHandle = await iconAtlasTask;
        InitThemeIcon();

        themeBg = await themeBgTask;
        firecracker = await firecrackerTask;

    }

    private Dictionary<int, Sprite> _themeIcons = new();
    private void InitThemeIcon()
    {
        var allAssetObjects = iconAtlasHandle.GetSubAssetObjects<Sprite>();
        var index = 0;
        foreach (var assetObject in allAssetObjects)
        {
            _themeIcons[index] = assetObject;
            index++;
        }
    }

    public Sprite GetThemeIcon(int iconIndex)
    {
        return _themeIcons[iconIndex];
    }

    async private UniTask InitPool()
    {
        var poolNames = PoolNames.GetNames();

        Dictionary<string, UniTask<GameObject>> tasks = new Dictionary<string, UniTask<GameObject>>();
        for (int i = 0; i < poolNames.Length; i++)
        {
            var poolName = poolNames[i];
            tasks.Add(poolName, LoadPrefabAsync(poolName));
        }
        await UniTask.WhenAll(tasks.Values.ToArray());

        PoolMgr.Inst.Clear();
        PoolMgr.Inst.CreatePool(await tasks[PoolNames.Pool_Explosion], PoolNames.Pool_Explosion, 2, 10, true);
        // PoolMgr.Inst.CreatePool(await tasks[PoolNames.Pool_CircleNum], PoolNames.Pool_CircleNum, 4, 10, true);
        PoolMgr.Inst.CreatePool(await tasks[PoolNames.Pool_EffectIceClick], PoolNames.Pool_EffectIceClick, 2, 10);
        PoolMgr.Inst.CreatePool(await tasks[PoolNames.Pool_EffectIceCrush], PoolNames.Pool_EffectIceCrush, 2, 10);
        PoolMgr.Inst.CreatePool(await tasks[PoolNames.Pool_EffectVineClick], PoolNames.Pool_EffectVineClick, 2, 10);
        PoolMgr.Inst.CreatePool(await tasks[PoolNames.Pool_EffectVineCrush], PoolNames.Pool_EffectVineCrush, 2, 10);
        PoolMgr.Inst.CreatePool(await tasks[PoolNames.Pool_EffectWoodClick], PoolNames.Pool_EffectWoodClick, 2, 10);
        PoolMgr.Inst.CreatePool(await tasks[PoolNames.Pool_EffectStoneClick], PoolNames.Pool_EffectStoneClick, 2, 10);
        PoolMgr.Inst.CreatePool(await tasks[PoolNames.Pool_Firecracker], PoolNames.Pool_Firecracker, 6, 12);
        PoolMgr.Inst.CreatePool(await tasks[PoolNames.Pool_EffectFireworksBoom], PoolNames.Pool_EffectFireworksBoom, 8, 10);
        PoolMgr.Inst.CreatePool(await tasks[PoolNames.Pool_EffectTileSelected], PoolNames.Pool_EffectTileSelected, 2, 10);
        PoolMgr.Inst.CreatePool(await tasks[PoolNames.Pool_EffectFlash], PoolNames.Pool_EffectFlash, 4, 8);
        PoolMgr.Inst.CreatePool(await tasks[PoolNames.Pool_EffectFlashPoint], PoolNames.Pool_EffectFlashPoint, 4, 8);
        PoolMgr.Inst.CreatePool(await tasks[PoolNames.Pool_EffectFlashTurn], PoolNames.Pool_EffectFlashTurn, 4, 8);
        PoolMgr.Inst.CreatePool(await tasks[PoolNames.Pool_EffectShinePink], PoolNames.Pool_EffectShinePink, 2, 6);
        PoolMgr.Inst.CreatePool(await tasks[PoolNames.Pool_EffectTileBroken], PoolNames.Pool_EffectTileBroken, 2, 10);
    }

    public PoolItem CreateExplosion() { return PoolMgr.Inst.Get(PoolNames.Pool_Explosion); }
    // public CircleNum CreateCircleNum() { return PoolMgr.Inst.Get<CircleNum>(PoolNames.Pool_CircleNum); }
    public PoolItem CreateFirecracker() { return PoolMgr.Inst.Get(PoolNames.Pool_Firecracker); }
    public PoolItem CreateFirecrackerExplosion() { return PoolMgr.Inst.Get(PoolNames.Pool_EffectFireworksBoom); }
    public PoolItem GetPoolItem(string poolName)
    {
        return PoolMgr.Inst.Get(poolName);
    }

    public void Dispose()
    {
        // 清理对象池
        PoolMgr.Inst.Clear();

        // 清理纹理引用
        _blockTextures.Clear();
        // themeTexture = null;
        themeBg = null;
    }

    public async UniTask LoadBlockTexture(int type, string path)
    {
        _blockTextures[type] = await LoadTextureAsync(path);
    }
    public Texture GetBlockTexture(int type)
    {
        if (_blockTextures.TryGetValue(type, out var texture))
        {
            return texture;
        }
        return null;
    }

    private UniTask<GameObject> LoadPrefabAsync(string path)
    {
        var tcs = new UniTaskCompletionSource<GameObject>();
        AssetBundleManager.LoadPrefab(path, (tex) => tcs.TrySetResult(tex));
        return tcs.Task;
    }

    private UniTask<Texture> LoadTextureAsync(string path)
    {
        var tcs = new UniTaskCompletionSource<Texture>();
        AssetBundleManager.LoadTexture(path, (tex) => tcs.TrySetResult(tex));
        return tcs.Task;
    }

    private UniTask<Sprite> LoadSpriteAsync(string path)
    {
        var tcs = new UniTaskCompletionSource<Sprite>();
        AssetBundleManager.LoadSprite(path, (sprite) => tcs.TrySetResult(sprite));
        return tcs.Task;
    }

    private UniTask<SubAssetsOperationHandle> LoadSpriteAtlasAsync(string path)
    {
        var tcs = new UniTaskCompletionSource<SubAssetsOperationHandle>();
        AssetBundleManager.LoadSpriteAtlas(path, (sprite) => tcs.TrySetResult(sprite));
        return tcs.Task;
    }
}
